
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/utils</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> src/utils</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">14.57% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>275/1887</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">10% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>1/10</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0.6% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>1/165</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">14.57% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>275/1887</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="AuthoritiesUtils.ts"><a href="AuthoritiesUtils.ts.html">AuthoritiesUtils.ts</a></td>
	<td data-value="72.72" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 72%"></div><div class="cover-empty" style="width: 28%"></div></div>
	</td>
	<td data-value="72.72" class="pct medium">72.72%</td>
	<td data-value="11" class="abs medium">8/11</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="72.72" class="pct medium">72.72%</td>
	<td data-value="11" class="abs medium">8/11</td>
	</tr>

<tr>
	<td class="file low" data-value="CaseSystemPaymentStoreUtils.ts"><a href="CaseSystemPaymentStoreUtils.ts.html">CaseSystemPaymentStoreUtils.ts</a></td>
	<td data-value="6.26" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 6%"></div><div class="cover-empty" style="width: 94%"></div></div>
	</td>
	<td data-value="6.26" class="pct low">6.26%</td>
	<td data-value="415" class="abs low">26/415</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="22" class="abs low">0/22</td>
	<td data-value="6.26" class="pct low">6.26%</td>
	<td data-value="415" class="abs low">26/415</td>
	</tr>

<tr>
	<td class="file low" data-value="ClaimCoverageStoreUtil.ts"><a href="ClaimCoverageStoreUtil.ts.html">ClaimCoverageStoreUtil.ts</a></td>
	<td data-value="8.77" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 8%"></div><div class="cover-empty" style="width: 92%"></div></div>
	</td>
	<td data-value="8.77" class="pct low">8.77%</td>
	<td data-value="114" class="abs low">10/114</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="8" class="abs low">0/8</td>
	<td data-value="8.77" class="pct low">8.77%</td>
	<td data-value="114" class="abs low">10/114</td>
	</tr>

<tr>
	<td class="file low" data-value="ClaimLossUtils.ts"><a href="ClaimLossUtils.ts.html">ClaimLossUtils.ts</a></td>
	<td data-value="10.14" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 10%"></div><div class="cover-empty" style="width: 90%"></div></div>
	</td>
	<td data-value="10.14" class="pct low">10.14%</td>
	<td data-value="335" class="abs low">34/335</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="34" class="abs low">0/34</td>
	<td data-value="10.14" class="pct low">10.14%</td>
	<td data-value="335" class="abs low">34/335</td>
	</tr>

<tr>
	<td class="file low" data-value="ClaimPartyUtils.ts"><a href="ClaimPartyUtils.ts.html">ClaimPartyUtils.ts</a></td>
	<td data-value="16.66" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 16%"></div><div class="cover-empty" style="width: 84%"></div></div>
	</td>
	<td data-value="16.66" class="pct low">16.66%</td>
	<td data-value="42" class="abs low">7/42</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="3" class="abs low">0/3</td>
	<td data-value="16.66" class="pct low">16.66%</td>
	<td data-value="42" class="abs low">7/42</td>
	</tr>

<tr>
	<td class="file low" data-value="CoverageUtils.tsx"><a href="CoverageUtils.tsx.html">CoverageUtils.tsx</a></td>
	<td data-value="10.37" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 10%"></div><div class="cover-empty" style="width: 90%"></div></div>
	</td>
	<td data-value="10.37" class="pct low">10.37%</td>
	<td data-value="135" class="abs low">14/135</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="12" class="abs low">0/12</td>
	<td data-value="10.37" class="pct low">10.37%</td>
	<td data-value="135" class="abs low">14/135</td>
	</tr>

<tr>
	<td class="file low" data-value="CurrentUserUtils.ts"><a href="CurrentUserUtils.ts.html">CurrentUserUtils.ts</a></td>
	<td data-value="45.45" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 45%"></div><div class="cover-empty" style="width: 55%"></div></div>
	</td>
	<td data-value="45.45" class="pct low">45.45%</td>
	<td data-value="11" class="abs low">5/11</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="45.45" class="pct low">45.45%</td>
	<td data-value="11" class="abs low">5/11</td>
	</tr>

<tr>
	<td class="file low" data-value="CustomerSearchUtils.ts"><a href="CustomerSearchUtils.ts.html">CustomerSearchUtils.ts</a></td>
	<td data-value="25.26" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 25%"></div><div class="cover-empty" style="width: 75%"></div></div>
	</td>
	<td data-value="25.26" class="pct low">25.26%</td>
	<td data-value="95" class="abs low">24/95</td>
	<td data-value="10" class="pct low">10%</td>
	<td data-value="10" class="abs low">1/10</td>
	<td data-value="20" class="pct low">20%</td>
	<td data-value="5" class="abs low">1/5</td>
	<td data-value="25.26" class="pct low">25.26%</td>
	<td data-value="95" class="abs low">24/95</td>
	</tr>

<tr>
	<td class="file low" data-value="CustomerUtils.ts"><a href="CustomerUtils.ts.html">CustomerUtils.ts</a></td>
	<td data-value="6.92" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 6%"></div><div class="cover-empty" style="width: 94%"></div></div>
	</td>
	<td data-value="6.92" class="pct low">6.92%</td>
	<td data-value="130" class="abs low">9/130</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="7" class="abs low">0/7</td>
	<td data-value="6.92" class="pct low">6.92%</td>
	<td data-value="130" class="abs low">9/130</td>
	</tr>

<tr>
	<td class="file low" data-value="DeductionsUtils.ts"><a href="DeductionsUtils.ts.html">DeductionsUtils.ts</a></td>
	<td data-value="17.56" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 17%"></div><div class="cover-empty" style="width: 83%"></div></div>
	</td>
	<td data-value="17.56" class="pct low">17.56%</td>
	<td data-value="74" class="abs low">13/74</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="8" class="abs low">0/8</td>
	<td data-value="17.56" class="pct low">17.56%</td>
	<td data-value="74" class="abs low">13/74</td>
	</tr>

<tr>
	<td class="file low" data-value="EntityLink.ts"><a href="EntityLink.ts.html">EntityLink.ts</a></td>
	<td data-value="34.93" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 34%"></div><div class="cover-empty" style="width: 66%"></div></div>
	</td>
	<td data-value="34.93" class="pct low">34.93%</td>
	<td data-value="83" class="abs low">29/83</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="9" class="abs low">0/9</td>
	<td data-value="34.93" class="pct low">34.93%</td>
	<td data-value="83" class="abs low">29/83</td>
	</tr>

<tr>
	<td class="file low" data-value="EventCaseUtils.ts"><a href="EventCaseUtils.ts.html">EventCaseUtils.ts</a></td>
	<td data-value="21.42" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 21%"></div><div class="cover-empty" style="width: 79%"></div></div>
	</td>
	<td data-value="21.42" class="pct low">21.42%</td>
	<td data-value="14" class="abs low">3/14</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="21.42" class="pct low">21.42%</td>
	<td data-value="14" class="abs low">3/14</td>
	</tr>

<tr>
	<td class="file high" data-value="HasPrivilegeContext.ts"><a href="HasPrivilegeContext.ts.html">HasPrivilegeContext.ts</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="5" class="abs high">5/5</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="5" class="abs high">5/5</td>
	</tr>

<tr>
	<td class="file low" data-value="KrakenUtils.ts"><a href="KrakenUtils.ts.html">KrakenUtils.ts</a></td>
	<td data-value="42.85" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 42%"></div><div class="cover-empty" style="width: 58%"></div></div>
	</td>
	<td data-value="42.85" class="pct low">42.85%</td>
	<td data-value="7" class="abs low">3/7</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="42.85" class="pct low">42.85%</td>
	<td data-value="7" class="abs low">3/7</td>
	</tr>

<tr>
	<td class="file low" data-value="PartyInformationUtils.ts"><a href="PartyInformationUtils.ts.html">PartyInformationUtils.ts</a></td>
	<td data-value="35.29" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 35%"></div><div class="cover-empty" style="width: 65%"></div></div>
	</td>
	<td data-value="35.29" class="pct low">35.29%</td>
	<td data-value="17" class="abs low">6/17</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="2" class="abs low">0/2</td>
	<td data-value="35.29" class="pct low">35.29%</td>
	<td data-value="17" class="abs low">6/17</td>
	</tr>

<tr>
	<td class="file low" data-value="PaymentsUtils.ts"><a href="PaymentsUtils.ts.html">PaymentsUtils.ts</a></td>
	<td data-value="13.68" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 13%"></div><div class="cover-empty" style="width: 87%"></div></div>
	</td>
	<td data-value="13.68" class="pct low">13.68%</td>
	<td data-value="95" class="abs low">13/95</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="9" class="abs low">0/9</td>
	<td data-value="13.68" class="pct low">13.68%</td>
	<td data-value="95" class="abs low">13/95</td>
	</tr>

<tr>
	<td class="file low" data-value="RenderUtils.tsx"><a href="RenderUtils.tsx.html">RenderUtils.tsx</a></td>
	<td data-value="27.27" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 27%"></div><div class="cover-empty" style="width: 73%"></div></div>
	</td>
	<td data-value="27.27" class="pct low">27.27%</td>
	<td data-value="11" class="abs low">3/11</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="27.27" class="pct low">27.27%</td>
	<td data-value="11" class="abs low">3/11</td>
	</tr>

<tr>
	<td class="file medium" data-value="ScrollableTableUtils.ts"><a href="ScrollableTableUtils.ts.html">ScrollableTableUtils.ts</a></td>
	<td data-value="55.55" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 55%"></div><div class="cover-empty" style="width: 45%"></div></div>
	</td>
	<td data-value="55.55" class="pct medium">55.55%</td>
	<td data-value="9" class="abs medium">5/9</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="2" class="abs low">0/2</td>
	<td data-value="55.55" class="pct medium">55.55%</td>
	<td data-value="9" class="abs medium">5/9</td>
	</tr>

<tr>
	<td class="file low" data-value="SortingUtils.ts"><a href="SortingUtils.ts.html">SortingUtils.ts</a></td>
	<td data-value="17.24" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 17%"></div><div class="cover-empty" style="width: 83%"></div></div>
	</td>
	<td data-value="17.24" class="pct low">17.24%</td>
	<td data-value="58" class="abs low">10/58</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="5" class="abs low">0/5</td>
	<td data-value="17.24" class="pct low">17.24%</td>
	<td data-value="58" class="abs low">10/58</td>
	</tr>

<tr>
	<td class="file low" data-value="ValidationUtils.ts"><a href="ValidationUtils.ts.html">ValidationUtils.ts</a></td>
	<td data-value="22.16" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 22%"></div><div class="cover-empty" style="width: 78%"></div></div>
	</td>
	<td data-value="22.16" class="pct low">22.16%</td>
	<td data-value="185" class="abs low">41/185</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="31" class="abs low">0/31</td>
	<td data-value="22.16" class="pct low">22.16%</td>
	<td data-value="185" class="abs low">41/185</td>
	</tr>

<tr>
	<td class="file low" data-value="VendorsUtils.ts"><a href="VendorsUtils.ts.html">VendorsUtils.ts</a></td>
	<td data-value="23.07" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 23%"></div><div class="cover-empty" style="width: 77%"></div></div>
	</td>
	<td data-value="23.07" class="pct low">23.07%</td>
	<td data-value="13" class="abs low">3/13</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="2" class="abs low">0/2</td>
	<td data-value="23.07" class="pct low">23.07%</td>
	<td data-value="13" class="abs low">3/13</td>
	</tr>

<tr>
	<td class="file low" data-value="eobUtils.ts"><a href="eobUtils.ts.html">eobUtils.ts</a></td>
	<td data-value="11.11" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 11%"></div><div class="cover-empty" style="width: 89%"></div></div>
	</td>
	<td data-value="11.11" class="pct low">11.11%</td>
	<td data-value="27" class="abs low">3/27</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="11.11" class="pct low">11.11%</td>
	<td data-value="27" class="abs low">3/27</td>
	</tr>

<tr>
	<td class="file high" data-value="index.ts"><a href="index.ts.html">index.ts</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-08-04T07:36:28.707Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    