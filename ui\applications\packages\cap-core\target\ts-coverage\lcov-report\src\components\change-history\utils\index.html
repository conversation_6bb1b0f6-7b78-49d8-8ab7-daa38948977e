
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/components/change-history/utils</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../../prettify.css" />
    <link rel="stylesheet" href="../../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../../index.html">All files</a> src/components/change-history/utils</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">20.92% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>236/1128</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>8/8</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">6.34% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>4/63</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">20.92% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>236/1128</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="columns.tsx"><a href="columns.tsx.html">columns.tsx</a></td>
	<td data-value="3.1" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 3%"></div><div class="cover-empty" style="width: 97%"></div></div>
	</td>
	<td data-value="3.1" class="pct low">3.1%</td>
	<td data-value="193" class="abs low">6/193</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="6" class="abs low">0/6</td>
	<td data-value="3.1" class="pct low">3.1%</td>
	<td data-value="193" class="abs low">6/193</td>
	</tr>

<tr>
	<td class="file low" data-value="filter.ts"><a href="filter.ts.html">filter.ts</a></td>
	<td data-value="20.83" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 20%"></div><div class="cover-empty" style="width: 80%"></div></div>
	</td>
	<td data-value="20.83" class="pct low">20.83%</td>
	<td data-value="24" class="abs low">5/24</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="20.83" class="pct low">20.83%</td>
	<td data-value="24" class="abs low">5/24</td>
	</tr>

<tr>
	<td class="file low" data-value="filterTag.tsx"><a href="filterTag.tsx.html">filterTag.tsx</a></td>
	<td data-value="7.69" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 7%"></div><div class="cover-empty" style="width: 93%"></div></div>
	</td>
	<td data-value="7.69" class="pct low">7.69%</td>
	<td data-value="39" class="abs low">3/39</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="7.69" class="pct low">7.69%</td>
	<td data-value="39" class="abs low">3/39</td>
	</tr>

<tr>
	<td class="file high" data-value="model-patch.ts"><a href="model-patch.ts.html">model-patch.ts</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="50" class="abs high">50/50</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="8" class="abs high">8/8</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="4" class="abs high">4/4</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="50" class="abs high">50/50</td>
	</tr>

<tr>
	<td class="file low" data-value="model.ts"><a href="model.ts.html">model.ts</a></td>
	<td data-value="33.02" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 33%"></div><div class="cover-empty" style="width: 67%"></div></div>
	</td>
	<td data-value="33.02" class="pct low">33.02%</td>
	<td data-value="109" class="abs low">36/109</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="13" class="abs low">0/13</td>
	<td data-value="33.02" class="pct low">33.02%</td>
	<td data-value="109" class="abs low">36/109</td>
	</tr>

<tr>
	<td class="file low" data-value="rows.tsx"><a href="rows.tsx.html">rows.tsx</a></td>
	<td data-value="3.68" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 3%"></div><div class="cover-empty" style="width: 97%"></div></div>
	</td>
	<td data-value="3.68" class="pct low">3.68%</td>
	<td data-value="515" class="abs low">19/515</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="25" class="abs low">0/25</td>
	<td data-value="3.68" class="pct low">3.68%</td>
	<td data-value="515" class="abs low">19/515</td>
	</tr>

<tr>
	<td class="file high" data-value="types.ts"><a href="types.ts.html">types.ts</a></td>
	<td data-value="82.6" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 82%"></div><div class="cover-empty" style="width: 18%"></div></div>
	</td>
	<td data-value="82.6" class="pct high">82.6%</td>
	<td data-value="138" class="abs high">114/138</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="12" class="abs low">0/12</td>
	<td data-value="82.6" class="pct high">82.6%</td>
	<td data-value="138" class="abs high">114/138</td>
	</tr>

<tr>
	<td class="file low" data-value="useHistoryTableFilters.tsx"><a href="useHistoryTableFilters.tsx.html">useHistoryTableFilters.tsx</a></td>
	<td data-value="5" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 5%"></div><div class="cover-empty" style="width: 95%"></div></div>
	</td>
	<td data-value="5" class="pct low">5%</td>
	<td data-value="60" class="abs low">3/60</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="5" class="pct low">5%</td>
	<td data-value="60" class="abs low">3/60</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-08-04T07:36:29.382Z
            </div>
        <script src="../../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../../sorter.js"></script>
        <script src="../../../../block-navigation.js"></script>
    </body>
</html>
    