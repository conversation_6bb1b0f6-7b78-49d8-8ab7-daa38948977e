/*
 * Copyright © 2016-2018 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import React, {useRef} from 'react'
import {render, screen, fireEvent} from '@testing-library/react'
import {CustomerType} from '@eisgroup/cap-services'
import {SearchCustomerIndividualView} from '../../../src/components/search-table/SearchCustomerComponent'
import {DrawerFormStateType, PreferredContactTypes} from '../../../src'
import {initLocalization} from '../../support/TestUtils'
import {LocalizationUtils, resources} from '@eisgroup/i18n'
import {FormApi} from '@eisgroup/form'

const mockProps = {
    loadItems: vi.fn(),
    setSelectedResult: vi.fn(),
    setAddPaymentMethodDrawerVisible: vi.fn(),
    setSearchAllCustomerFilter: vi.fn(),
    searchAllCustomerFilter: {},
    updateCurrentPageNumber: vi.fn(),
    setLoadingStatus: vi.fn(),
    customerType: CustomerType.Individual,
    setCustomerSearchAllResults: vi.fn(),
    clearFilterSearch: vi.fn(),
    isLoading: false,
    setFormParty: vi.fn(),
    temporaryFormParty: {} as any,
    drawerFormState: DrawerFormStateType.Create,
    afterSaveCustomerSuccess: vi.fn(),
    preferredContactMethod: PreferredContactTypes.PHONE,
    associatedWithOptions: [],
    showDisabledSelfRelationship: false,
    hideRelationshipToParticipant: false,
    onCustomerSelect: vi.fn(),
    customerSearchResults: [],
    searchCustomerByRegistryTypeId: vi.fn(),
    setIsShowTable: vi.fn(),
    filterFormRef: {current: null} as React.RefObject<FormApi>
} as any

describe('SearchCustomerIndividualView', () => {
    beforeAll(async () => {
        await initLocalization
        LocalizationUtils.addResourceBundles(resources)
    })

    beforeEach(() => {
        vi.clearAllMocks()
    })

    it('renders without crashing', () => {
        render(<SearchCustomerIndividualView {...mockProps} />)
        expect(screen.getByText('Add New Party')).toBeInTheDocument()
    })

    it('should handle filter search when isFilterSearch is true', () => {
        const TestComponent = () => {
            const filterFormRef = useRef<FormApi>(null)
            return <SearchCustomerIndividualView {...mockProps} filterFormRef={filterFormRef} />
        }

        const {container} = render(<TestComponent />)

        const filterIcon = container.querySelector('.party-customer-search-filter-icon')
        if (filterIcon) {
            fireEvent.click(filterIcon)
        }

        const mockHandleFilterSearch = vi.fn((result, query, body, isFilterSearch) => {
            if (isFilterSearch) {
                mockProps.setSearchAllCustomerFilter({
                    ...mockProps.searchAllCustomerFilter,
                    text: query,
                    body: body || mockProps.searchAllCustomerFilter?.body
                })
                mockProps.setLoadingStatus(true)
                mockProps.setIsShowTable(true)
                mockProps.updateCurrentPageNumber(1)
                mockProps.loadItems({
                    ...mockProps.searchAllCustomerFilter,
                    text: query,
                    body: body || mockProps.searchAllCustomerFilter?.body
                })
                mockProps.setSelectedResult({})
            }
        })

        mockHandleFilterSearch(null, 'test query', {}, true)

        expect(mockProps.setSearchAllCustomerFilter).toHaveBeenCalled()
        expect(mockProps.setLoadingStatus).toHaveBeenCalledWith(true)
        expect(mockProps.setIsShowTable).toHaveBeenCalledWith(true)
        expect(mockProps.updateCurrentPageNumber).toHaveBeenCalledWith(1)
        expect(mockProps.loadItems).toHaveBeenCalled()
        expect(mockProps.setSelectedResult).toHaveBeenCalledWith({})
    })

    it('should not handle filter search when isFilterSearch is false', () => {
        const mockHandleFilterSearch = vi.fn((result, query, body, isFilterSearch) => {
            if (!isFilterSearch) {
                return
            }
        })

        mockHandleFilterSearch(null, 'test query', {}, false)

        expect(mockProps.setSearchAllCustomerFilter).not.toHaveBeenCalled()
        expect(mockProps.loadItems).not.toHaveBeenCalled()
    })

    it('should handle search input with length > 2', () => {
        const TestComponent = () => {
            const filterFormRef = useRef<FormApi>(null)
            return <SearchCustomerIndividualView {...mockProps} filterFormRef={filterFormRef} />
        }

        render(<TestComponent />)

        const mockGetSearchInput = vi.fn((e: string) => {
            mockProps.updateCurrentPageNumber(1)

            if (e.length > 2) {
                mockProps.setSearchAllCustomerFilter({
                    ...mockProps.searchAllCustomerFilter,
                    text: e
                })
                mockProps.loadItems({
                    ...mockProps.searchAllCustomerFilter,
                    text: e
                })
            }
        })

        mockGetSearchInput('test')

        expect(mockProps.updateCurrentPageNumber).toHaveBeenCalledWith(1)
        expect(mockProps.setSearchAllCustomerFilter).toHaveBeenCalledWith({
            ...mockProps.searchAllCustomerFilter,
            text: 'test'
        })
        expect(mockProps.loadItems).toHaveBeenCalledWith({
            ...mockProps.searchAllCustomerFilter,
            text: 'test'
        })
    })

    it('should not call search APIs when input length <= 2', () => {
        const mockGetSearchInput = vi.fn((e: string) => {
            mockProps.updateCurrentPageNumber(1)

            if (e.length > 2) {
                mockProps.setSearchAllCustomerFilter({
                    ...mockProps.searchAllCustomerFilter,
                    text: e
                })
                mockProps.loadItems({
                    ...mockProps.searchAllCustomerFilter,
                    text: e
                })
            }
        })

        mockGetSearchInput('te')

        expect(mockProps.updateCurrentPageNumber).toHaveBeenCalledWith(1)
        expect(mockProps.setSearchAllCustomerFilter).not.toHaveBeenCalled()
        expect(mockProps.loadItems).not.toHaveBeenCalled()
    })

    it('should render vendor layout when customerType is vendor', () => {
        const vendorProps = {
            ...mockProps,
            customerType: CustomerType.VendorIndividual
        }

        const TestComponent = () => {
            const filterFormRef = useRef<FormApi>(null)
            return <SearchCustomerIndividualView {...vendorProps} filterFormRef={filterFormRef} />
        }

        const {container} = render(<TestComponent />)

        expect(container.querySelector('.relation-component-create-search-form-vendors')).toBeInTheDocument()
    })

    it('should handle disabled state correctly', () => {
        const disabledProps = {
            ...mockProps,
            disabled: true
        }

        const TestComponent = () => {
            const filterFormRef = useRef<FormApi>(null)
            return <SearchCustomerIndividualView {...disabledProps} filterFormRef={filterFormRef} />
        }

        const {container} = render(<TestComponent />)

        const filterIcon = container.querySelector('.party-customer-search-filter-icon')
        expect(filterIcon).toBeInTheDocument()
    })
})
