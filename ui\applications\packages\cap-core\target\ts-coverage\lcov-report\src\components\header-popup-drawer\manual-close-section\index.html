
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/components/header-popup-drawer/manual-close-section</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../../prettify.css" />
    <link rel="stylesheet" href="../../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../../index.html">All files</a> src/components/header-popup-drawer/manual-close-section</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">5.13% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>28/545</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/0</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/0</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">5.13% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>28/545</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="ActiveClaimList.tsx"><a href="ActiveClaimList.tsx.html">ActiveClaimList.tsx</a></td>
	<td data-value="3.61" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 3%"></div><div class="cover-empty" style="width: 97%"></div></div>
	</td>
	<td data-value="3.61" class="pct low">3.61%</td>
	<td data-value="83" class="abs low">3/83</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="3.61" class="pct low">3.61%</td>
	<td data-value="83" class="abs low">3/83</td>
	</tr>

<tr>
	<td class="file low" data-value="ActiveTaskList.tsx"><a href="ActiveTaskList.tsx.html">ActiveTaskList.tsx</a></td>
	<td data-value="5.66" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 5%"></div><div class="cover-empty" style="width: 95%"></div></div>
	</td>
	<td data-value="5.66" class="pct low">5.66%</td>
	<td data-value="53" class="abs low">3/53</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="5.66" class="pct low">5.66%</td>
	<td data-value="53" class="abs low">3/53</td>
	</tr>

<tr>
	<td class="file low" data-value="IncompletePaymentList.tsx"><a href="IncompletePaymentList.tsx.html">IncompletePaymentList.tsx</a></td>
	<td data-value="7.27" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 7%"></div><div class="cover-empty" style="width: 93%"></div></div>
	</td>
	<td data-value="7.27" class="pct low">7.27%</td>
	<td data-value="55" class="abs low">4/55</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="7.27" class="pct low">7.27%</td>
	<td data-value="55" class="abs low">4/55</td>
	</tr>

<tr>
	<td class="file low" data-value="IncompletePremiumWaiverApprovalPeriodList.tsx"><a href="IncompletePremiumWaiverApprovalPeriodList.tsx.html">IncompletePremiumWaiverApprovalPeriodList.tsx</a></td>
	<td data-value="9.3" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 9%"></div><div class="cover-empty" style="width: 91%"></div></div>
	</td>
	<td data-value="9.3" class="pct low">9.3%</td>
	<td data-value="43" class="abs low">4/43</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="9.3" class="pct low">9.3%</td>
	<td data-value="43" class="abs low">4/43</td>
	</tr>

<tr>
	<td class="file low" data-value="ManualCloseWrapper.tsx"><a href="ManualCloseWrapper.tsx.html">ManualCloseWrapper.tsx</a></td>
	<td data-value="4.3" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 4%"></div><div class="cover-empty" style="width: 96%"></div></div>
	</td>
	<td data-value="4.3" class="pct low">4.3%</td>
	<td data-value="93" class="abs low">4/93</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="4.3" class="pct low">4.3%</td>
	<td data-value="93" class="abs low">4/93</td>
	</tr>

<tr>
	<td class="file low" data-value="UnpaidCoverageList.tsx"><a href="UnpaidCoverageList.tsx.html">UnpaidCoverageList.tsx</a></td>
	<td data-value="3.47" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 3%"></div><div class="cover-empty" style="width: 97%"></div></div>
	</td>
	<td data-value="3.47" class="pct low">3.47%</td>
	<td data-value="115" class="abs low">4/115</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="3.47" class="pct low">3.47%</td>
	<td data-value="115" class="abs low">4/115</td>
	</tr>

<tr>
	<td class="file low" data-value="UnpostedPaymentList.tsx"><a href="UnpostedPaymentList.tsx.html">UnpostedPaymentList.tsx</a></td>
	<td data-value="4.22" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 4%"></div><div class="cover-empty" style="width: 96%"></div></div>
	</td>
	<td data-value="4.22" class="pct low">4.22%</td>
	<td data-value="71" class="abs low">3/71</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="4.22" class="pct low">4.22%</td>
	<td data-value="71" class="abs low">3/71</td>
	</tr>

<tr>
	<td class="file low" data-value="UnprocessedBalanceList.tsx"><a href="UnprocessedBalanceList.tsx.html">UnprocessedBalanceList.tsx</a></td>
	<td data-value="9.37" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 9%"></div><div class="cover-empty" style="width: 91%"></div></div>
	</td>
	<td data-value="9.37" class="pct low">9.37%</td>
	<td data-value="32" class="abs low">3/32</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="9.37" class="pct low">9.37%</td>
	<td data-value="32" class="abs low">3/32</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-08-04T07:13:40.492Z
            </div>
        <script src="../../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../../sorter.js"></script>
        <script src="../../../../block-navigation.js"></script>
    </body>
</html>
    