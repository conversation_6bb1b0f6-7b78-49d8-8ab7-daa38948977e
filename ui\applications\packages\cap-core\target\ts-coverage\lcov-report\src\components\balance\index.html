
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/components/balance</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../prettify.css" />
    <link rel="stylesheet" href="../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../index.html">All files</a> src/components/balance</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">4.66% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>53/1135</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/0</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/4</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">4.66% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>53/1135</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="BalanceActivities.tsx"><a href="BalanceActivities.tsx.html">BalanceActivities.tsx</a></td>
	<td data-value="3.14" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 3%"></div><div class="cover-empty" style="width: 97%"></div></div>
	</td>
	<td data-value="3.14" class="pct low">3.14%</td>
	<td data-value="127" class="abs low">4/127</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="3.14" class="pct low">3.14%</td>
	<td data-value="127" class="abs low">4/127</td>
	</tr>

<tr>
	<td class="file low" data-value="BalanceListCommonExpandRow.tsx"><a href="BalanceListCommonExpandRow.tsx.html">BalanceListCommonExpandRow.tsx</a></td>
	<td data-value="5.66" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 5%"></div><div class="cover-empty" style="width: 95%"></div></div>
	</td>
	<td data-value="5.66" class="pct low">5.66%</td>
	<td data-value="53" class="abs low">3/53</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="5.66" class="pct low">5.66%</td>
	<td data-value="53" class="abs low">3/53</td>
	</tr>

<tr>
	<td class="file low" data-value="BalanceListDisabilityExpandRow.tsx"><a href="BalanceListDisabilityExpandRow.tsx.html">BalanceListDisabilityExpandRow.tsx</a></td>
	<td data-value="5.08" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 5%"></div><div class="cover-empty" style="width: 95%"></div></div>
	</td>
	<td data-value="5.08" class="pct low">5.08%</td>
	<td data-value="59" class="abs low">3/59</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="5.08" class="pct low">5.08%</td>
	<td data-value="59" class="abs low">3/59</td>
	</tr>

<tr>
	<td class="file low" data-value="BalanceListExpand.tsx"><a href="BalanceListExpand.tsx.html">BalanceListExpand.tsx</a></td>
	<td data-value="17.64" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 17%"></div><div class="cover-empty" style="width: 83%"></div></div>
	</td>
	<td data-value="17.64" class="pct low">17.64%</td>
	<td data-value="17" class="abs low">3/17</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="17.64" class="pct low">17.64%</td>
	<td data-value="17" class="abs low">3/17</td>
	</tr>

<tr>
	<td class="file low" data-value="BalanceListLifeExpandRow.tsx"><a href="BalanceListLifeExpandRow.tsx.html">BalanceListLifeExpandRow.tsx</a></td>
	<td data-value="3.94" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 3%"></div><div class="cover-empty" style="width: 97%"></div></div>
	</td>
	<td data-value="3.94" class="pct low">3.94%</td>
	<td data-value="76" class="abs low">3/76</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="3.94" class="pct low">3.94%</td>
	<td data-value="76" class="abs low">3/76</td>
	</tr>

<tr>
	<td class="file low" data-value="BalanceTable.tsx"><a href="BalanceTable.tsx.html">BalanceTable.tsx</a></td>
	<td data-value="2.45" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 2%"></div><div class="cover-empty" style="width: 98%"></div></div>
	</td>
	<td data-value="2.45" class="pct low">2.45%</td>
	<td data-value="366" class="abs low">9/366</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="2.45" class="pct low">2.45%</td>
	<td data-value="366" class="abs low">9/366</td>
	</tr>

<tr>
	<td class="file low" data-value="PaymentAppliedWithholdings.tsx"><a href="PaymentAppliedWithholdings.tsx.html">PaymentAppliedWithholdings.tsx</a></td>
	<td data-value="7.89" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 7%"></div><div class="cover-empty" style="width: 93%"></div></div>
	</td>
	<td data-value="7.89" class="pct low">7.89%</td>
	<td data-value="76" class="abs low">6/76</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="7.89" class="pct low">7.89%</td>
	<td data-value="76" class="abs low">6/76</td>
	</tr>

<tr>
	<td class="file low" data-value="PaymentWithholdingTable.tsx"><a href="PaymentWithholdingTable.tsx.html">PaymentWithholdingTable.tsx</a></td>
	<td data-value="3.63" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 3%"></div><div class="cover-empty" style="width: 97%"></div></div>
	</td>
	<td data-value="3.63" class="pct low">3.63%</td>
	<td data-value="110" class="abs low">4/110</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="3.63" class="pct low">3.63%</td>
	<td data-value="110" class="abs low">4/110</td>
	</tr>

<tr>
	<td class="file low" data-value="RecalculationPayments.tsx"><a href="RecalculationPayments.tsx.html">RecalculationPayments.tsx</a></td>
	<td data-value="2.87" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 2%"></div><div class="cover-empty" style="width: 98%"></div></div>
	</td>
	<td data-value="2.87" class="pct low">2.87%</td>
	<td data-value="139" class="abs low">4/139</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="2.87" class="pct low">2.87%</td>
	<td data-value="139" class="abs low">4/139</td>
	</tr>

<tr>
	<td class="file low" data-value="WithholdingAmount.tsx"><a href="WithholdingAmount.tsx.html">WithholdingAmount.tsx</a></td>
	<td data-value="11.42" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 11%"></div><div class="cover-empty" style="width: 89%"></div></div>
	</td>
	<td data-value="11.42" class="pct low">11.42%</td>
	<td data-value="35" class="abs low">4/35</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="11.42" class="pct low">11.42%</td>
	<td data-value="35" class="abs low">4/35</td>
	</tr>

<tr>
	<td class="file low" data-value="WithholdingPercentage.tsx"><a href="WithholdingPercentage.tsx.html">WithholdingPercentage.tsx</a></td>
	<td data-value="20" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 20%"></div><div class="cover-empty" style="width: 80%"></div></div>
	</td>
	<td data-value="20" class="pct low">20%</td>
	<td data-value="20" class="abs low">4/20</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="20" class="pct low">20%</td>
	<td data-value="20" class="abs low">4/20</td>
	</tr>

<tr>
	<td class="file low" data-value="utils.tsx"><a href="utils.tsx.html">utils.tsx</a></td>
	<td data-value="10.52" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 10%"></div><div class="cover-empty" style="width: 90%"></div></div>
	</td>
	<td data-value="10.52" class="pct low">10.52%</td>
	<td data-value="57" class="abs low">6/57</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="4" class="abs low">0/4</td>
	<td data-value="10.52" class="pct low">10.52%</td>
	<td data-value="57" class="abs low">6/57</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-08-04T07:23:58.479Z
            </div>
        <script src="../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../sorter.js"></script>
        <script src="../../../block-navigation.js"></script>
    </body>
</html>
    