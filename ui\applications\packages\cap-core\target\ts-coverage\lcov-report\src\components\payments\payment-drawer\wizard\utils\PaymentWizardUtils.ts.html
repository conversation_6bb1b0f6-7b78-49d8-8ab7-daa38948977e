
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/components/payments/payment-drawer/wizard/utils/PaymentWizardUtils.ts</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../../../../prettify.css" />
    <link rel="stylesheet" href="../../../../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../../../../index.html">All files</a> / <a href="index.html">src/components/payments/payment-drawer/wizard/utils</a> PaymentWizardUtils.ts</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">8.33% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>2/24</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/0</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">8.33% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>2/24</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a></td><td class="line-coverage quiet"><span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import {getFormStateValues} from '@eisgroup/builder'
import {CapPaymentTemplate} from '@eisgroup/cap-financial-models'
import {CaseSystemService, ClaimLoss} from '@eisgroup/cap-services'
import {toJS} from 'mobx'
import {PAYMENT_DETAIL_FORM_ID} from '../../steps/PaymentDetailStep'
import CapPaymentAllocationTemplateEntity = CapPaymentTemplate.CapPaymentAllocationTemplateEntity
import CapPaymentDetailsTemplateEntity = CapPaymentTemplate.CapPaymentDetailsTemplateEntity
import CapManualEOBRemarksInput = CapPaymentTemplate.CapManualEOBRemarksInput
import {Allocation, ICaseSystem} from '../../../../../common/Types'
import {CaseSystemPaymentStore} from '../../../../../common/store'
import {GetPaymentMethodAndCheckAddressIdFunc} from '../PaymentWizard'
import {convertSettlementsArrToStr, formatAllocationsToSettlements} from './PaymentWizardSettlements'
&nbsp;
export const isDetailFormChanged = <span class="fstat-no" title="function not covered" >(props: {</span>
    paymentStore: CaseSystemPaymentStore&lt;ICaseSystem, CaseSystemService&gt;
    associatedClaims: ClaimLoss[]
    getPaymentMethodIdAndCheckAddressId: GetPaymentMethodAndCheckAddressIdFunc
    getManualEOBRemarks: (allocation: Allocation, source: string) =&gt; CapManualEOBRemarksInput[]
}) =&gt; {
<span class="cstat-no" title="statement not covered" >    const {paymentStore, associatedClaims, getPaymentMethodIdAndCheckAddressId, getManualEOBRemarks} = props</span>
<span class="cstat-no" title="statement not covered" >    const {buildPaymentScheduleInput, allocations, payee, representBeneficiary} = getFormStateValues(</span>
<span class="cstat-no" title="statement not covered" >        PAYMENT_DETAIL_FORM_ID</span>
    ) as any
<span class="cstat-no" title="statement not covered" >    const curFormValue = {</span>
<span class="cstat-no" title="statement not covered" >        ...buildPaymentScheduleInput,</span>
<span class="cstat-no" title="statement not covered" >        payee: {_uri: representBeneficiary ?? payee},</span>
<span class="cstat-no" title="statement not covered" >        settlements: formatAllocationsToSettlements({</span>
<span class="cstat-no" title="statement not covered" >            allocations,</span>
<span class="cstat-no" title="statement not covered" >            originalSettlements: paymentStore.paymentTemplate?.buildPaymentScheduleInput?.settlements ?? [],</span>
<span class="cstat-no" title="statement not covered" >            associatedClaims,</span>
<span class="cstat-no" title="statement not covered" >            getPaymentMethodIdAndCheckAddressId,</span>
<span class="cstat-no" title="statement not covered" >            getManualEOBRemarks</span>
<span class="cstat-no" title="statement not covered" >        }) as CapPaymentAllocationTemplateEntity[]</span>
<span class="cstat-no" title="statement not covered" >    } as CapPaymentDetailsTemplateEntity</span>
<span class="cstat-no" title="statement not covered" >    const formValueInStore = toJS(paymentStore.paymentTemplate.buildPaymentScheduleInput)</span>
<span class="cstat-no" title="statement not covered" >    const payeeChanged = curFormValue.payee?._uri !== paymentStore.currentPayee</span>
<span class="cstat-no" title="statement not covered" >    const descChanged = curFormValue?.description !== formValueInStore?.description</span>
<span class="cstat-no" title="statement not covered" >    const allocationChanged =</span>
<span class="cstat-no" title="statement not covered" >        convertSettlementsArrToStr(curFormValue?.settlements) !==</span>
<span class="cstat-no" title="statement not covered" >        convertSettlementsArrToStr(formValueInStore?.settlements || [])</span>
<span class="cstat-no" title="statement not covered" >    return payeeChanged || descChanged || allocationChanged</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-08-04T07:13:40.492Z
            </div>
        <script src="../../../../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../../../../sorter.js"></script>
        <script src="../../../../../../block-navigation.js"></script>
    </body>
</html>
    