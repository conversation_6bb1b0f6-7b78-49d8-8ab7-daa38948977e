
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/components/party-details-form/search</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../../prettify.css" />
    <link rel="stylesheet" href="../../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../../index.html">All files</a> src/components/party-details-form/search</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">48.68% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>74/152</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">50% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>3/6</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">33.33% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>2/6</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">48.68% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>74/152</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="PartySearch.tsx"><a href="PartySearch.tsx.html">PartySearch.tsx</a></td>
	<td data-value="97.36" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 97%"></div><div class="cover-empty" style="width: 3%"></div></div>
	</td>
	<td data-value="97.36" class="pct high">97.36%</td>
	<td data-value="38" class="abs high">37/38</td>
	<td data-value="50" class="pct medium">50%</td>
	<td data-value="4" class="abs medium">2/4</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	<td data-value="97.36" class="pct high">97.36%</td>
	<td data-value="38" class="abs high">37/38</td>
	</tr>

<tr>
	<td class="file low" data-value="PartySearchContainer.tsx"><a href="PartySearchContainer.tsx.html">PartySearchContainer.tsx</a></td>
	<td data-value="32.45" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 32%"></div><div class="cover-empty" style="width: 68%"></div></div>
	</td>
	<td data-value="32.45" class="pct low">32.45%</td>
	<td data-value="114" class="abs low">37/114</td>
	<td data-value="50" class="pct medium">50%</td>
	<td data-value="2" class="abs medium">1/2</td>
	<td data-value="20" class="pct low">20%</td>
	<td data-value="5" class="abs low">1/5</td>
	<td data-value="32.45" class="pct low">32.45%</td>
	<td data-value="114" class="abs low">37/114</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-08-04T07:23:58.479Z
            </div>
        <script src="../../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../../sorter.js"></script>
        <script src="../../../../block-navigation.js"></script>
    </body>
</html>
    