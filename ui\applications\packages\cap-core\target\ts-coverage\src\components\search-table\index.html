
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/components/search-table</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../prettify.css" />
    <link rel="stylesheet" href="../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../index.html">All files</a> src/components/search-table</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">38.78% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>465/1199</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">84.21% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>16/19</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">21.87% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>7/32</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">38.78% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>465/1199</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="CustomerFilterSearch.tsx"><a href="CustomerFilterSearch.tsx.html">CustomerFilterSearch.tsx</a></td>
	<td data-value="64.89" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 64%"></div><div class="cover-empty" style="width: 36%"></div></div>
	</td>
	<td data-value="64.89" class="pct medium">64.89%</td>
	<td data-value="188" class="abs medium">122/188</td>
	<td data-value="62.5" class="pct medium">62.5%</td>
	<td data-value="8" class="abs medium">5/8</td>
	<td data-value="11.11" class="pct low">11.11%</td>
	<td data-value="18" class="abs low">2/18</td>
	<td data-value="64.89" class="pct medium">64.89%</td>
	<td data-value="188" class="abs medium">122/188</td>
	</tr>

<tr>
	<td class="file medium" data-value="SearchCustomerComponent.tsx"><a href="SearchCustomerComponent.tsx.html">SearchCustomerComponent.tsx</a></td>
	<td data-value="72.67" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 72%"></div><div class="cover-empty" style="width: 28%"></div></div>
	</td>
	<td data-value="72.67" class="pct medium">72.67%</td>
	<td data-value="161" class="abs medium">117/161</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="9" class="abs high">9/9</td>
	<td data-value="37.5" class="pct low">37.5%</td>
	<td data-value="8" class="abs low">3/8</td>
	<td data-value="72.67" class="pct medium">72.67%</td>
	<td data-value="161" class="abs medium">117/161</td>
	</tr>

<tr>
	<td class="file low" data-value="SearchCustomerOrganizationAndIndividual.tsx"><a href="SearchCustomerOrganizationAndIndividual.tsx.html">SearchCustomerOrganizationAndIndividual.tsx</a></td>
	<td data-value="1.44" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 1%"></div><div class="cover-empty" style="width: 99%"></div></div>
	</td>
	<td data-value="1.44" class="pct low">1.44%</td>
	<td data-value="207" class="abs low">3/207</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="1.44" class="pct low">1.44%</td>
	<td data-value="207" class="abs low">3/207</td>
	</tr>

<tr>
	<td class="file low" data-value="SearchCustomerView.tsx"><a href="SearchCustomerView.tsx.html">SearchCustomerView.tsx</a></td>
	<td data-value="1.05" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 1%"></div><div class="cover-empty" style="width: 99%"></div></div>
	</td>
	<td data-value="1.05" class="pct low">1.05%</td>
	<td data-value="379" class="abs low">4/379</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="1.05" class="pct low">1.05%</td>
	<td data-value="379" class="abs low">4/379</td>
	</tr>

<tr>
	<td class="file low" data-value="addNewPartyBtn.tsx"><a href="addNewPartyBtn.tsx.html">addNewPartyBtn.tsx</a></td>
	<td data-value="49.43" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 49%"></div><div class="cover-empty" style="width: 51%"></div></div>
	</td>
	<td data-value="49.43" class="pct low">49.43%</td>
	<td data-value="89" class="abs low">44/89</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	<td data-value="25" class="pct low">25%</td>
	<td data-value="4" class="abs low">1/4</td>
	<td data-value="49.43" class="pct low">49.43%</td>
	<td data-value="89" class="abs low">44/89</td>
	</tr>

<tr>
	<td class="file high" data-value="constant.ts"><a href="constant.ts.html">constant.ts</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="29" class="abs high">29/29</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="29" class="abs high">29/29</td>
	</tr>

<tr>
	<td class="file high" data-value="searchPartyStore.ts"><a href="searchPartyStore.ts.html">searchPartyStore.ts</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="146" class="abs high">146/146</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="146" class="abs high">146/146</td>
	</tr>

<tr>
	<td class="file empty" data-value="utils.ts"><a href="utils.ts.html">utils.ts</a></td>
	<td data-value="0" class="pic empty">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct empty">0%</td>
	<td data-value="0" class="abs empty">0/0</td>
	<td data-value="0" class="pct empty">0%</td>
	<td data-value="1" class="abs empty">1/1</td>
	<td data-value="0" class="pct empty">0%</td>
	<td data-value="1" class="abs empty">1/1</td>
	<td data-value="0" class="pct empty">0%</td>
	<td data-value="0" class="abs empty">0/0</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-08-04T07:23:57.427Z
            </div>
        <script src="../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../sorter.js"></script>
        <script src="../../../block-navigation.js"></script>
    </body>
</html>
    