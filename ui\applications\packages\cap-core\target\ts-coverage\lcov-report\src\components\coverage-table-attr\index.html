
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/components/coverage-table-attr</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../prettify.css" />
    <link rel="stylesheet" href="../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../index.html">All files</a> src/components/coverage-table-attr</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">30.07% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>308/1024</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/0</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/7</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">30.07% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>308/1024</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="AnesthesiaBurnRecurring.tsx"><a href="AnesthesiaBurnRecurring.tsx.html">AnesthesiaBurnRecurring.tsx</a></td>
	<td data-value="8" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 8%"></div><div class="cover-empty" style="width: 92%"></div></div>
	</td>
	<td data-value="8" class="pct low">8%</td>
	<td data-value="50" class="abs low">4/50</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="8" class="pct low">8%</td>
	<td data-value="50" class="abs low">4/50</td>
	</tr>

<tr>
	<td class="file low" data-value="DateRange.tsx"><a href="DateRange.tsx.html">DateRange.tsx</a></td>
	<td data-value="5.97" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 5%"></div><div class="cover-empty" style="width: 95%"></div></div>
	</td>
	<td data-value="5.97" class="pct low">5.97%</td>
	<td data-value="67" class="abs low">4/67</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="5.97" class="pct low">5.97%</td>
	<td data-value="67" class="abs low">4/67</td>
	</tr>

<tr>
	<td class="file low" data-value="Eligibility.tsx"><a href="Eligibility.tsx.html">Eligibility.tsx</a></td>
	<td data-value="1.52" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 1%"></div><div class="cover-empty" style="width: 99%"></div></div>
	</td>
	<td data-value="1.52" class="pct low">1.52%</td>
	<td data-value="263" class="abs low">4/263</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="1.52" class="pct low">1.52%</td>
	<td data-value="263" class="abs low">4/263</td>
	</tr>

<tr>
	<td class="file low" data-value="FormulaContent.tsx"><a href="FormulaContent.tsx.html">FormulaContent.tsx</a></td>
	<td data-value="5.17" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 5%"></div><div class="cover-empty" style="width: 95%"></div></div>
	</td>
	<td data-value="5.17" class="pct low">5.17%</td>
	<td data-value="58" class="abs low">3/58</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="5.17" class="pct low">5.17%</td>
	<td data-value="58" class="abs low">3/58</td>
	</tr>

<tr>
	<td class="file low" data-value="GrossAmount.tsx"><a href="GrossAmount.tsx.html">GrossAmount.tsx</a></td>
	<td data-value="5.97" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 5%"></div><div class="cover-empty" style="width: 95%"></div></div>
	</td>
	<td data-value="5.97" class="pct low">5.97%</td>
	<td data-value="67" class="abs low">4/67</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="5.97" class="pct low">5.97%</td>
	<td data-value="67" class="abs low">4/67</td>
	</tr>

<tr>
	<td class="file low" data-value="GrossAmountFormula.tsx"><a href="GrossAmountFormula.tsx.html">GrossAmountFormula.tsx</a></td>
	<td data-value="11.11" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 11%"></div><div class="cover-empty" style="width: 89%"></div></div>
	</td>
	<td data-value="11.11" class="pct low">11.11%</td>
	<td data-value="27" class="abs low">3/27</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="11.11" class="pct low">11.11%</td>
	<td data-value="27" class="abs low">3/27</td>
	</tr>

<tr>
	<td class="file low" data-value="IncidentDate.tsx"><a href="IncidentDate.tsx.html">IncidentDate.tsx</a></td>
	<td data-value="10.81" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 10%"></div><div class="cover-empty" style="width: 90%"></div></div>
	</td>
	<td data-value="10.81" class="pct low">10.81%</td>
	<td data-value="37" class="abs low">4/37</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="10.81" class="pct low">10.81%</td>
	<td data-value="37" class="abs low">4/37</td>
	</tr>

<tr>
	<td class="file low" data-value="NumberOfUnits.tsx"><a href="NumberOfUnits.tsx.html">NumberOfUnits.tsx</a></td>
	<td data-value="5.76" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 5%"></div><div class="cover-empty" style="width: 95%"></div></div>
	</td>
	<td data-value="5.76" class="pct low">5.76%</td>
	<td data-value="52" class="abs low">3/52</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="5.76" class="pct low">5.76%</td>
	<td data-value="52" class="abs low">3/52</td>
	</tr>

<tr>
	<td class="file low" data-value="ProofOfLossReceivedDate.tsx"><a href="ProofOfLossReceivedDate.tsx.html">ProofOfLossReceivedDate.tsx</a></td>
	<td data-value="12.12" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 12%"></div><div class="cover-empty" style="width: 88%"></div></div>
	</td>
	<td data-value="12.12" class="pct low">12.12%</td>
	<td data-value="33" class="abs low">4/33</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="12.12" class="pct low">12.12%</td>
	<td data-value="33" class="abs low">4/33</td>
	</tr>

<tr>
	<td class="file high" data-value="UnpaidAmountAndRemainLimitPopover.tsx"><a href="UnpaidAmountAndRemainLimitPopover.tsx.html">UnpaidAmountAndRemainLimitPopover.tsx</a></td>
	<td data-value="99.25" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 99%"></div><div class="cover-empty" style="width: 1%"></div></div>
	</td>
	<td data-value="99.25" class="pct high">99.25%</td>
	<td data-value="270" class="abs high">268/270</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="99.25" class="pct high">99.25%</td>
	<td data-value="270" class="abs high">268/270</td>
	</tr>

<tr>
	<td class="file low" data-value="Utils.tsx"><a href="Utils.tsx.html">Utils.tsx</a></td>
	<td data-value="7" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 7%"></div><div class="cover-empty" style="width: 93%"></div></div>
	</td>
	<td data-value="7" class="pct low">7%</td>
	<td data-value="100" class="abs low">7/100</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="5" class="abs low">0/5</td>
	<td data-value="7" class="pct low">7%</td>
	<td data-value="100" class="abs low">7/100</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-08-04T07:13:40.492Z
            </div>
        <script src="../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../sorter.js"></script>
        <script src="../../../block-navigation.js"></script>
    </body>
</html>
    