
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/common</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> src/common</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">63.74% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>1266/1986</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>4/4</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">1.2% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>1/83</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">63.74% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>1266/1986</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="IconTypeMapper.tsx"><a href="IconTypeMapper.tsx.html">IconTypeMapper.tsx</a></td>
	<td data-value="78.57" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 78%"></div><div class="cover-empty" style="width: 22%"></div></div>
	</td>
	<td data-value="78.57" class="pct medium">78.57%</td>
	<td data-value="14" class="abs medium">11/14</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="78.57" class="pct medium">78.57%</td>
	<td data-value="14" class="abs medium">11/14</td>
	</tr>

<tr>
	<td class="file low" data-value="PartyFormRules.ts"><a href="PartyFormRules.ts.html">PartyFormRules.ts</a></td>
	<td data-value="17.02" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 17%"></div><div class="cover-empty" style="width: 83%"></div></div>
	</td>
	<td data-value="17.02" class="pct low">17.02%</td>
	<td data-value="47" class="abs low">8/47</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="6" class="abs low">0/6</td>
	<td data-value="17.02" class="pct low">17.02%</td>
	<td data-value="47" class="abs low">8/47</td>
	</tr>

<tr>
	<td class="file high" data-value="Types.ts"><a href="Types.ts.html">Types.ts</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="50" class="abs high">50/50</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="4" class="abs high">4/4</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="50" class="abs high">50/50</td>
	</tr>

<tr>
	<td class="file high" data-value="constants.ts"><a href="constants.ts.html">constants.ts</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="488" class="abs high">488/488</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="488" class="abs high">488/488</td>
	</tr>

<tr>
	<td class="file high" data-value="package-class-names.ts"><a href="package-class-names.ts.html">package-class-names.ts</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="599" class="abs high">599/599</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="599" class="abs high">599/599</td>
	</tr>

<tr>
	<td class="file low" data-value="useInitialClaimWorkBench.ts"><a href="useInitialClaimWorkBench.ts.html">useInitialClaimWorkBench.ts</a></td>
	<td data-value="12.12" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 12%"></div><div class="cover-empty" style="width: 88%"></div></div>
	</td>
	<td data-value="12.12" class="pct low">12.12%</td>
	<td data-value="33" class="abs low">4/33</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="12.12" class="pct low">12.12%</td>
	<td data-value="33" class="abs low">4/33</td>
	</tr>

<tr>
	<td class="file low" data-value="utils.ts"><a href="utils.ts.html">utils.ts</a></td>
	<td data-value="14.03" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 14%"></div><div class="cover-empty" style="width: 86%"></div></div>
	</td>
	<td data-value="14.03" class="pct low">14.03%</td>
	<td data-value="755" class="abs low">106/755</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="74" class="abs low">0/74</td>
	<td data-value="14.03" class="pct low">14.03%</td>
	<td data-value="755" class="abs low">106/755</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-08-04T07:23:58.479Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    